from flask import Blueprint, render_template, request, jsonify, current_app, session, send_file
from src.backend.blueprints.auth.decorators import login_epr
from src.backend.blueprints.translator_bot.translation_service import DirectTranslationService
from src.backend.blueprints.translator_bot.progress_tracker import progress_tracker
from src.backend.models import User
from src import db
import os
import pandas as pd
import tempfile
from werkzeug.utils import secure_filename


translator_bot_routes = Blueprint('translator_bot_routes', __name__, template_folder='templates')


def get_translator_upload_dir(user_id: str) -> str:
    """Get the upload directory path for a specific user"""
    temp_base_dir = os.path.join(tempfile.gettempdir(), 'translator-bot-uploads')
    os.makedirs(temp_base_dir, exist_ok=True)
    upload_dir = os.path.join(temp_base_dir, f'user_{user_id}')
    os.makedirs(upload_dir, exist_ok=True)
    return upload_dir


def generate_translated_filename(original_filename: str, target_language: str) -> str:
    """Generate a translated filename based on original name and target language"""
    if not original_filename:
        return f"translated_file_{target_language}.xlsx"

    # Get language name mapping
    language_names = {
        'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
        'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
        'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
        'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
        'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
    }

    language_name = language_names.get(target_language, target_language)

    # Split filename and extension
    name_parts = original_filename.rsplit('.', 1)
    if len(name_parts) == 2:
        base_name, extension = name_parts
        return f"{base_name}_translated_{language_name}.{extension}"
    else:
        # No extension found
        return f"{original_filename}_translated_{language_name}"


@translator_bot_routes.route('/')
@login_epr
def index():
    """Main translation tool page"""
    current_app.logger.info("Translation tool accessed")
    return render_template('translator_bot/translation_tool.html')


@translator_bot_routes.route('/api/upload', methods=['POST'])
@login_epr
def upload_file():
    """Handle file upload for translation"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'.xlsx', '.pptx', '.docx'}
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type'}), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Clean up any existing files for this user before uploading new one
        try:
            translation_service = DirectTranslationService(user_id)
            translation_service.cleanup()
            current_app.logger.info(f"Cleaned up existing files for user {user_id}")
        except Exception as e:
            current_app.logger.warning(f"Cleanup warning for user {user_id}: {e}")

        # Get upload directory for this user
        upload_dir = get_translator_upload_dir(user_id)

        
        # For Excel files, save and process with independent translator system
        if file_extension == '.xlsx':
            
            # Save file with user ID
            filename = secure_filename(f"data_{user_id}.xlsx")
            file_path = os.path.join(upload_dir, filename)
            file.seek(0)  # Reset file pointer after reading
            file.save(file_path)
            
            # Read Excel file to get column information
            try:
                df = pd.read_excel(file_path, nrows=5)  # Preview first 5 rows
                columns = df.columns.tolist()

                # Convert preview data safely
                preview_data = []
                for _, row in df.head().iterrows():
                    row_dict = {}
                    for col in columns:
                        value = row[col]
                        # Handle NaN and other non-serializable values
                        if pd.isna(value):
                            row_dict[col] = None
                        elif isinstance(value, (int, float, str, bool)):
                            row_dict[col] = value
                        else:
                            row_dict[col] = str(value)
                    preview_data.append(row_dict)

                # Get total rows safely
                try:
                    total_df = pd.read_excel(file_path)
                    total_rows = len(total_df)
                except:
                    total_rows = len(df)

                return jsonify({
                    'success': True,
                    'filename': file.filename,
                    'type': file_extension,
                    'columns': columns,
                    'preview': preview_data,
                    'total_rows': total_rows
                })
            except Exception as e:
                current_app.logger.error(f"Excel processing error: {e}")
                return jsonify({'error': f'Error reading Excel file: {str(e)}'}), 400
        
        else:
            # For other file types, use existing logic
            return jsonify({
                'success': True,
                'filename': file.filename,
                'size': len(file.read()),
                'type': file_extension
            })
        
    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        import traceback
        current_app.logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


@translator_bot_routes.route('/api/translate', methods=['POST'])
@login_epr
def translate_document():
    """Handle document translation request"""
    import zipfile
    try:
        data = request.get_json()
        current_app.logger.info(f"Translation request received: {data}")

        # Validate required fields
        if not data or 'target_languages' not in data or not isinstance(data['target_languages'], list) or not data['target_languages']:
            current_app.logger.error("Invalid request: Target languages are required")
            return jsonify({'error': 'Target languages are required'}), 400

        target_languages = data['target_languages']
        source_language = data.get('source_language', 'auto')
        selected_columns = data.get('selected_columns', [])
        file_type = data.get('file_type', '')
        original_filename = data.get('original_filename', '')
        file_context = data.get('file_context', '').strip()
        session_id = data.get('session_id')

        current_app.logger.info(f"Translation parameters - Languages: {target_languages}, Columns: {selected_columns}, File: {original_filename}")

        # Get user ID from session
        user = session.get('user')
        if not user:
            current_app.logger.error("User not authenticated")
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        current_app.logger.info(f"Processing translation for user {user_id}")

        # Store translation info in session for download
        session['translation_info'] = {
            'original_filename': original_filename,
            'target_languages': target_languages
        }

        # Handle Excel files
        if file_type == '.xlsx':
            current_app.logger.info(f"Processing Excel file for user {user_id}")
            try:
                # Use session ID from request for progress tracking
                if session_id:
                    current_app.logger.info(f"Using session ID for progress tracking: {session_id}")

                # Initialize DirectTranslationService
                translation_service = DirectTranslationService(user_id)
                current_app.logger.info(f"DirectTranslationService initialized for user {user_id}")

                # Translate selected columns or all columns, passing file_context and session_id
                if selected_columns:
                    current_app.logger.info(f"Translating selected columns: {selected_columns}")
                    result = translation_service.translate_multiple_languages(
                        selected_columns,
                        target_languages,
                        max_rows=200,
                        file_context=file_context,
                        session_id=session_id
                    )
                else:
                    # Get all columns and translate them
                    all_columns = translation_service.get_excel_columns()
                    current_app.logger.info(f"All columns found: {all_columns}")
                    # TODO: Filter out non-text columns if needed
                    text_columns = [col for col in all_columns if col]
                    current_app.logger.info(f"Text columns to translate: {text_columns}")
                    result = translation_service.translate_multiple_languages(
                        text_columns,
                        target_languages,
                        max_rows=200,
                        file_context=file_context,
                        session_id=session_id
                    )

                current_app.logger.info(f"Translation service completed for user {user_id}")
                current_app.logger.info(f"Translation result success: {result.get('success', False)}")

                # If multiple languages, create a zip file
                if result['success'] and len(target_languages) > 1:
                    current_app.logger.info(f"Creating zip file for {len(target_languages)} languages")
                    try:
                        upload_dir = get_translator_upload_dir(user_id)
                        zip_filename = f"translated_files_{user_id}.zip"
                        zip_filepath = os.path.join(upload_dir, zip_filename)
                        current_app.logger.info(f"Zip file path: {zip_filepath}")

                        files_added = 0
                        with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                            for lang in target_languages:
                                translated_file_path = os.path.join(upload_dir, f"data_{user_id}_{lang}.xlsx")
                                if os.path.exists(translated_file_path):
                                    archive_name = generate_translated_filename(original_filename, lang)
                                    zipf.write(translated_file_path, arcname=archive_name)
                                    files_added += 1
                                    current_app.logger.info(f"Added {archive_name} to zip")
                                else:
                                    current_app.logger.warning(f"Translated file not found: {translated_file_path}")

                        current_app.logger.info(f"Created zip file with {files_added} files at {zip_filepath}")

                        # Return zip file info in result JSON (full path for download endpoint)
                        result['zip_file'] = zip_filepath

                    except Exception as e:
                        current_app.logger.error(f"Error creating zip file for user {user_id}: {e}")
               
                if result['success']:
                    return jsonify({
                        'success': True,
                        'message': 'Translation completed',
                        'status': 'completed',
                        'data': result,
                        'zip_file': result.get('zip_file'),
                        'columns_translated': selected_columns or 'all',
                        'session_id': session_id
                    })
                else:
                    return jsonify({'error': f'Translation failed: {result.get('error', 'Unknown error')}'})

            except Exception as e:
                current_app.logger.error(f"Direct translation error for user {user_id}: {e}")
                current_app.logger.error(f"Error details - File: {original_filename}, Languages: {target_languages}")
                return jsonify({'error': f'Excel translation failed: {str(e)}'}), 500
        else:
            # For other file types, implement other translation logic
            current_app.logger.info(f"Unsupported file type {file_type} for translation: {source_language} -> {target_languages}")
            current_app.logger.warning(f"User {user_id} attempted to translate unsupported file type: {file_type}")
            return jsonify({
                'success': False,
                'error': f'File type {file_type} is not supported yet'
            })
    except Exception as e:
        current_app.logger.error(f"General translation error for user {user_id}: {e}")
        current_app.logger.error(f"Request data: {data}")
        return jsonify({'error': 'Translation failed'}), 500
    


@translator_bot_routes.route('/api/download/<translation_id>')
@login_epr
def download_translated_file(translation_id):
    """Download translated file"""
    try:
        current_app.logger.info(f"Download request for translation_id: {translation_id}")

        user = session.get('user')
        if not user:
            current_app.logger.error("Download request from unauthenticated user")
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        current_app.logger.info(f"Download request from user {user_id}")

        # Get translation info from session
        translation_info = session.get('translation_info', {})
        original_filename = translation_info.get('original_filename', '')
        target_languages = translation_info.get('target_languages', [])

        current_app.logger.info(f"Translation info - Original: {original_filename}, Languages: {target_languages}")

        # Check if zip_file is requested
        zip_file = request.args.get('zip_file')
        if zip_file:
            upload_dir = get_translator_upload_dir(user_id)
            zip_path = os.path.join(upload_dir, zip_file)
            if os.path.exists(zip_path):
                from flask import send_file
                return send_file(
                    zip_path,
                    as_attachment=True,
                    download_name=zip_file,
                    mimetype='application/zip'
                )
            else:
                return jsonify({'error': 'Zip file not found'}), 404

        else:
            # If a specific language is requested, download that file
            requested_language = request.args.get('lang')
            if requested_language and requested_language in target_languages:
                translated_filename = generate_translated_filename(original_filename, requested_language)
                upload_dir = get_translator_upload_dir(user_id)
                file_path = os.path.join(upload_dir, f"data_{user_id}_{requested_language}.xlsx")
                if os.path.exists(file_path):
                    from flask import send_file
                    file_extension = translated_filename.lower().split('.')[-1] if '.' in translated_filename else 'xlsx'
                    mime_types = {
                        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    }
                    mimetype = mime_types.get(file_extension, 'application/octet-stream')
                    response = send_file(
                        file_path,
                        as_attachment=True,
                        download_name=translated_filename,
                        mimetype=mimetype
                    )
                    return response
                else:
                    return jsonify({'error': f'Translated file for {requested_language} not found'}), 404
            else:
                # No valid language specified or language not in target languages
                current_app.logger.error(f"Invalid or missing language parameter. Requested: {requested_language}, Available: {target_languages}")
                return jsonify({'error': 'Invalid or missing language parameter'}), 400
        
    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500


@translator_bot_routes.route('/api/preview', methods=['POST'])
@login_epr
def preview_translation():
    """Preview translation for Excel files"""
    try:
        data = request.get_json()
        current_app.logger.info(f"Preview request data: {data}")

        if not data or 'column_name' not in data or 'target_language' not in data:
            current_app.logger.error(f"Invalid preview request data: {data}")
            return jsonify({'error': 'Column name and target language are required'}), 400

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        column_name = data['column_name']
        target_language = data['target_language']

        # Initialize DirectTranslationService for preview
        translation_service = DirectTranslationService(user_id)

        # Get preview translation, pass file_context if present
        file_context = data.get('file_context', '').strip() if data.get('file_context') else None
        preview_result = translation_service.preview_translation(
            column_name,
            target_language,
            preview_rows=10,
            file_context=file_context
        )

        if preview_result['success']:
            # Create HTML table for preview
            preview_html = "<table class='table table-striped'><thead><tr><th>Row</th><th>Original</th><th>Translated</th></tr></thead><tbody>"
            for item in preview_result['preview_data']:
                preview_html += f"<tr><td>{item['row']}</td><td>{item['original']}</td><td>{item['translated']}</td></tr>"
            preview_html += "</tbody></table>"

            return jsonify({
                'success': True,
                'preview_html': preview_html,
                'total_rows': preview_result['total_rows_in_file'],
                'column': preview_result['column'],
                'target_language': preview_result['target_language']
            })
        else:
            return jsonify({'error': preview_result.get('error', 'Preview failed')}), 500

    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'error': 'Preview failed'}), 500


@translator_bot_routes.route('/api/columns', methods=['GET'])
@login_epr
def get_excel_columns():
    """Get available columns and row count from uploaded Excel file"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Initialize DirectTranslationService
        translation_service = DirectTranslationService(user_id)

        # Get Excel file info (columns and row count)
        excel_info = translation_service.get_excel_info()

        if excel_info['success']:
            return jsonify({
                'success': True,
                'columns': excel_info['columns'],
                'row_count': excel_info['row_count']
            })
        else:
            return jsonify({'error': excel_info.get('error', 'No columns found or file not uploaded')}), 404

    except Exception as e:
        current_app.logger.error(f"Get columns error: {e}")
        return jsonify({'error': 'Failed to get columns'}), 500


@translator_bot_routes.route('/api/version', methods=['GET'])
@login_epr
def get_version():
    """Get version information for translator bot"""
    try:
        from src.backend.utils.sys_utils import get_env_version
        return get_env_version()
    except Exception as e:
        current_app.logger.error(f"Version error: {e}")
        return jsonify({'error': 'Failed to get version'}), 500


@translator_bot_routes.route('/api/progress/<session_id>', methods=['GET'])
@login_epr
def get_translation_progress(session_id):
    """Get translation progress for a session"""
    try:
        current_app.logger.debug(f"Progress request for session: {session_id}")
        progress = progress_tracker.get_progress(session_id)
        if progress is None:
            current_app.logger.warning(f"Progress session not found: {session_id}")
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404

        response_data = {
            'success': True,
            'completed': progress['completed'],
            'total': progress['total'],
            'details': progress['details'],
            'elapsed_time': progress['elapsed_time'],
            'eta_seconds': progress.get('eta_seconds')
        }
        current_app.logger.debug(f"Returning progress: {response_data}")
        return jsonify(response_data)
    except Exception as e:
        current_app.logger.error(f"Progress tracking error: {e}")
        return jsonify({
            'success': False,
            'error': 'Error retrieving progress'
        }), 500


@translator_bot_routes.route('/api/changelog/preview', methods=['GET'])
@login_epr
def get_changelog_preview():
    """Get changelog preview for translator bot"""
    try:
        from src.backend.utils.sys_utils import stream_last_n_releases
        import os
        
        # Look for changelog file
        changelog_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', 'CHANGELOG.md')
        if os.path.exists(changelog_path):
            preview = stream_last_n_releases(changelog_path, 2)
            return preview, 200, {'Content-Type': 'text/plain; charset=utf-8'}
        else:
            return 'Changelog not found', 404
    except Exception as e:
        current_app.logger.error(f"Changelog error: {e}")
        return 'Error loading changelog', 500
